<div class="ui card fluid <?php echo(($is_alert ?? null) ? '' : 'statistics_card') ?>">
    <div class="content">
        <div class="header">
            <?php echo $this->e($name) ?> <i class="ui icon question circle primary-color has-tooltip right floated"
                                            style="color:black"
                                            data-variation="wide" data-title="<?php echo $this->e($tooltip_title) ?>"
                                            data-content="<?php echo $this->e($tooltip_content) ?>"></i>
            <?php
            $lapse = $lapse ?? false;
            if (!!$lapse) {
                include __DIR__ . '/../menus/lapse_buttons.php';
            }
            ?>
        </div>
    </div>

    <?php if ($is_alert ?? null) { ?>
        <div class="content ui center aligned">
            <div class="header" style="margin-top:10%">
                <svg class="ui tiny image" id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 128 128">
                    <defs>
                        <style>.cls-1 {
                                fill: #c3c8dd;
                            }</style>
                    </defs>
                    <title>no_connect1</title>
                    <path class="cls-1"
                          d="M108,106.26a2,2,0,0,1-1.74,1H82a2,2,0,0,1,0-4h20.75L64.37,36.79,26,103.24H41.33A7.29,7.29,0,0,1,42.67,102c.35-.27,1.26-1,1.29-1.27a.84.84,0,0,0-.17-.38,11,11,0,0,0-1.58-1.79,8,8,0,0,1-2.87-5,7.28,7.28,0,0,1,.35-3.45,7.45,7.45,0,0,1,8.29-5,10,10,0,0,1,5.33,2.73C54,88.42,54.69,89,55.08,89s.85-.47,1.46-1.51a11.17,11.17,0,0,1,.94-14.72L61,69.2a2,2,0,0,1,3-2.73l2,2,5.27-5.28a2,2,0,0,1,2.85,2.85l-5.27,5.28,5.77,5.77,5.28-5.28a2,2,0,0,1,2.85,2.85L77.5,80l2,2a2,2,0,0,1,0,2.84,2,2,0,0,1-2.72.12l-3.55,3.55a11.08,11.08,0,0,1-7.89,3.26,2,2,0,1,1,0-4,7.11,7.11,0,0,0,5-2.07L74,82.14,63.87,72.05,60.32,75.6a7.15,7.15,0,0,0,0,10.09,2,2,0,0,1,.51,2v0a1.7,1.7,0,0,1-.19.67c-1.39,2.87-3,4.38-5.07,4.63s-3.64-1-4.87-2.08a6.22,6.22,0,0,0-3.18-1.8,3.43,3.43,0,0,0-4,2.35A3.24,3.24,0,0,0,43.32,93,4.18,4.18,0,0,0,45,95.65a14.56,14.56,0,0,1,2.15,2.47,4.14,4.14,0,0,1,0,5.12H72.59a2,2,0,1,1,0,4H22.52a2,2,0,0,1-1.75-1,2.06,2.06,0,0,1,0-2l41.86-72.5a2,2,0,0,1,3.48,0L108,104.25A2,2,0,0,1,108,106.26Z"/>
                    <path class="cls-1"
                          d="M83.54,48.82a2,2,0,0,1-1.74-1L78.46,42A2,2,0,0,1,82,40l3.34,5.78a2,2,0,0,1-1.75,3Z"/>
                    <path class="cls-1"
                          d="M122.94,117.05H5.07a2,2,0,0,1-1.75-3L62.26,12A2,2,0,0,1,64,11h0a2,2,0,0,1,1.74,1L76.23,30.12a2,2,0,1,1-3.48,2L64,17,8.55,113h110.9L87.37,57.46a2,2,0,0,1,3.49-2L124.68,114a2,2,0,0,1-1.74,3Z"/>
                </svg>
            </div>
            <div class="ui statistic" style="margin-bottom:10%">
                <div class="label">
                    <?php echo $this->e($value) ?>
                </div>
                <br>
                <div class="content">
                    <?php echo $is_alert ?>
                </div>
            </div>
        </div>
    <?php } else { ?>
        <div class="content ui center aligned">
            <div class="ui statistic number_statistic">
                <div style="color:black" class="value">
                    <?php echo $this->e($value) ?>
                </div>
                <div class="label">
                    <?php echo $this->e($label) ?>
                    <?php if (isset($subLabel)) {
                        echo "<br/><div class=\"label\" style=\"
    font-weight: bold;
    text-transform: uppercase;
    line-height: 1.3;
    font-size: .6rem;
    color: rgba(0,0,0,0.6);
\">$subLabel</div>";
                    } ?>
                </div>
            </div>
        </div>
    <?php } ?>
</div>
