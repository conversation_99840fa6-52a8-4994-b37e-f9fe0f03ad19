function renderDoughnutStatistic(a,t,e,n,r,o,l){o=o||!1,l=l||!1;var d=document.getElementById(a),s=(t={datasets:[{data:t,backgroundColor:e,borderWidth:0,hoverBackgroundColor:["rgba(0, 0, 0, .6)","rgba(163, 230, 53, .6)","rgba(56, 247, 234, .6)","rgba(113, 90, 255, .6)","rgba(0, 0, 0, .6)","rgba(0, 0, 0, .4)","rgba(0, 0, 0, .2)"]}],labels:n},{cutoutPercentage:l?55:70,tooltips:{callbacks:{label:function(a,t){return t.labels[a.index]||""}}},legend:{display:!1,position:"bottom"},legendCallback:function(a){var t=[];t.push('<ul class="'+a.id+'-legend">');for(var e=0;e<a.data.datasets[0].data.length;e++)t.push('<li><i class="ui icon circle" style="color:'+a.data.datasets[0].backgroundColor[e]+'"></i> <span>'),a.data.labels[e]&&t.push(a.data.labels[e]),t.push("</span></li>");return t.push("</ul>"),t.join("")},plugins:{datalabels:{backgroundColor:"transparent",color:"white",font:{weight:"bold"},formatter:function(a){return l?Math.round(100*a)/100+(o?"":"%"):""}}}}),u=new Chart(d,{type:"doughnut",data:t,options:s});$("#"+r).parent().append(u.generateLegend())}