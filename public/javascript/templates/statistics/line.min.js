function renderLineStatistic(e,t,a,i,o,l){var s=document.getElementById(e).getContext("2d"),n=(t={labels:o,datasets:[{label:i,data:t,backgroundColor:a,borderColor:"rgba(0, 0, 0, 1)",pointBackgroundColor:"rgba(0, 0, 0, 1)",borderWidth:2,fill:"origin"}]},{scales:{yAxes:[{ticks:{min:0,beginAtZero:!0,stepSize:l}}],xAxes:[{ticks:{autoSkip:!0,maxTicksLimit:24}}]},maintainAspectRatio:!1,legend:{display:!1,position:"bottom"},plugins:{datalabels:{display:!1}},responsive:!0});Chart.defaults.global.defaultFontFamily="Lato,'Helvetica Neue',Arial,Helvetica,sans-serif",new Chart(s,{type:"line",data:t,options:n})}